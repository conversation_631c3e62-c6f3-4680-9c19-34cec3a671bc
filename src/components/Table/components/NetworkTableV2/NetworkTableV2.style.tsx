import styled, { css } from "styled-components";
import CircularProgress from "@mui/material/CircularProgress";
import React from "react";
import { VirtualTable } from "@devexpress/dx-react-grid-material-ui";
import Paper from "@mui/material/Paper";

export const CellStyled = styled(VirtualTable.Cell)``;

export const ContainerLoading = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  background: rgba(255, 255, 255, 0.3);
`;

export const CircularProgressStyled = styled(CircularProgress)`
  position: absolute;
  font-size: 20px;
  top: calc(45% - 10px);
  left: calc(50% - 10px);
`;

export const Loading = () => (
  <ContainerLoading data-test="table.loader">
    <CircularProgressStyled />
  </ContainerLoading>
);

export const RowStyled = styled(VirtualTable.Row)<{ isselected?: boolean; ishover?: boolean }>`
  &:hover {
    background-color: ${(p) => p.theme.hover} !important;
  }
  ${(p) =>
    p.isselected &&
    css`
      background-color: ${(p) => p?.theme?.rowSelected};
    `}
  ${(p) =>
    p.ishover &&
    css`
      cursor: pointer;
    `}
`;

export const ContentCell = styled.div<{ position?: string }>`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
  ${(p) =>
    p.position === "center" &&
    css`
      justify-content: center !important;
    `}
  ${(p) =>
    p.position === "left" &&
    css`
      justify-content: flex-start !important;
    `}
  ${(p) =>
    p.position === "right" &&
    css`
      justify-content: flex-end !important;
    `}
`;

export const CellName = styled.div`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: ${(p) => p.theme.textColor};
`;

export const getCellName = (value: any, filters: any) => {
  const isFind = filters.some((el: any) => el.value === value);
  if (isFind) {
    return (
      <CellName>
        <mark>{value}</mark>
      </CellName>
    );
  }
  return <CellName>{value}</CellName>;
};

export const PaperStyled = styled(Paper)`
  position: relative;
`;

export const Container = styled.div<{ isOverFlowHidden: boolean }>`
  width: 100%;
  height: 100%;
  //margin-top: 10px;
  overflow: auto;
  display: flex;
  flex-flow: column;
  min-height: 0;
  flex-grow: 1;
  .MuiIconButton-root {
    margin-left: 10px;
  }
  th {
    border-right: 1px solid ${(p) => p.theme.borderTh};
  }
  .MuiPaper-root {
    height: 100%;
    overflow-y: auto;
    background-color: ${(p) => p.theme.backgroundColorSecondary};
    & > div {
      height: 100%;
    }
  }
  .MuiTableCell-root {
    padding: 0 4px;
  }
  .MuiTableCell-head {
    height: 32px; //40px
    min-height: 32px;
    //display: flex;
    //align-items: center;
    padding: 0 10px;
    cursor: default;
    user-select: none;
    font-weight: bold;
    font-size: 14px; //14
    background-color: ${(p) => p.theme.backgroundColorSecondary};
    color: ${(p) => p.theme.textColor};
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    //position: relative;
    width: auto;
  }
  .MuiTableBody-root {
    .MuiTableRow-root {
      //&:nth-child(2n) {
      //  td {
      //    background-color: #f3f4f5;
      //  }
      //}
      td {
        height: 21px; //20 26 35
        min-height: 21px;
        max-height: 21px;
        padding: 0;
        overflow: ${(p) => (p.isOverFlowHidden ? "hidden" : "visible")};
        color: ${(p) => p.theme.textColor};
      }

      td > span {
        width: 20px; //20
        height: 20px;
      }

      [data-testid="ChevronRightIcon"] {
        color: ${(p) => p.theme.textColor};
      }

      // button {
      //   padding: 0;
      //   color: ${(p) => p.theme.textColor};
      //   height: 16px;
      // }
      button {
        padding: 0;
        color: ${(p) => p.theme.textColor};
      }

      // &:hover td {
      //   background-color: ${(p) => p.theme.hover} !important;
      // }
    }
  }
  //.TableTreeCell-container {
  //  //height: 20px;
  //  overflow: visible;
  //}
  //box-shadow: none;
  //border: none;
  //// .MuiPaper-root {
  //.MuiTableBody-root {
  //  box-shadow: none;
  //  border: none;
  //  .MuiTableRow-root {
  //    box-shadow: none;
  //    border: none;
  //  }
  //}
  .MuiPaper-root {
    box-shadow: none;
  }
  .MuiPaper-root {
    box-shadow: none;
  }
  .TableTreeContent-content {
    color: ${(p) => p.theme.textColor};
  }
  .TableNoDataCell-text {
    color: ${(p) => p.theme.textColor};
  }
  .TableTreeExpandButton-button {
    color: ${(p) => p.theme.textColor};
  }
  //.MuiTableBody-root {
  //  .MuiTableRow-root {
  //    //&:nth-child(2n) {
  //    //  td {
  //    //    background-color: #f3f4f5;
  //    //  }
  //    //}
  //     td {
  //      overflow: visible;
  //     }
  //
  //    //button {
  //    //  padding: 0;
  //    //}
`;
